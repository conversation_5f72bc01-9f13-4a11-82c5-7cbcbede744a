const amqp = require('amqplib');
require('dotenv').config();

// RabbitMQ configuration
const rabbitmqConfig = {
  url: process.env.RABBITMQ_URL || 'amqp://localhost:5672',
  exchange: process.env.RABBITMQ_EXCHANGE || 'analysis_exchange',
  queue: process.env.RABBITMQ_QUEUE || 'analysis_jobs_queue',
  routingKey: process.env.RABBITMQ_ROUTING_KEY || 'analysis.job'
};

// Create connection to RabbitMQ
const createConnection = async () => {
  try {
    const connection = await amqp.connect(rabbitmqConfig.url);
    console.log('RabbitMQ connected successfully');
    
    // Handle connection errors
    connection.on('error', (err) => {
      console.error('RabbitMQ connection error:', err);
    });
    
    connection.on('close', () => {
      console.log('RabbitMQ connection closed');
    });
    
    return connection;
  } catch (err) {
    console.error('Failed to connect to RabbitMQ:', err);
    throw err;
  }
};

// Create channel and setup exchange/queue
const setupChannel = async (connection) => {
  try {
    const channel = await connection.createChannel();
    
    // Assert exchange (create if doesn't exist)
    await channel.assertExchange(rabbitmqConfig.exchange, 'direct', {
      durable: true // Exchange survives broker restarts
    });
    
    // Assert queue (create if doesn't exist)
    await channel.assertQueue(rabbitmqConfig.queue, {
      durable: true // Queue survives broker restarts
    });
    
    // Bind queue to exchange
    await channel.bindQueue(
      rabbitmqConfig.queue,
      rabbitmqConfig.exchange,
      rabbitmqConfig.routingKey
    );
    
    console.log('RabbitMQ channel setup completed');
    return channel;
  } catch (err) {
    console.error('Failed to setup RabbitMQ channel:', err);
    throw err;
  }
};

// Publish message to queue
const publishMessage = async (message) => {
  let connection;
  let channel;
  
  try {
    // Create connection and channel
    connection = await createConnection();
    channel = await setupChannel(connection);
    
    // Convert message to buffer
    const messageBuffer = Buffer.from(JSON.stringify(message));
    
    // Publish message
    const published = channel.publish(
      rabbitmqConfig.exchange,
      rabbitmqConfig.routingKey,
      messageBuffer,
      {
        persistent: true, // Message survives broker restarts
        timestamp: Date.now(),
        messageId: message.profileId || 'unknown'
      }
    );
    
    if (published) {
      console.log('Message published successfully:', {
        exchange: rabbitmqConfig.exchange,
        routingKey: rabbitmqConfig.routingKey,
        messageId: message.profileId
      });
    } else {
      throw new Error('Failed to publish message to RabbitMQ');
    }
    
    // Wait for confirms (optional but recommended)
    await channel.waitForConfirms();
    
  } catch (err) {
    console.error('Error publishing message to RabbitMQ:', err);
    throw err;
  } finally {
    // Clean up resources
    try {
      if (channel) {
        await channel.close();
      }
      if (connection) {
        await connection.close();
      }
    } catch (closeErr) {
      console.error('Error closing RabbitMQ connection:', closeErr);
    }
  }
};

// Test RabbitMQ connection
const testConnection = async () => {
  let connection;
  try {
    connection = await createConnection();
    const channel = await setupChannel(connection);
    await channel.close();
    console.log('RabbitMQ connection test successful');
  } catch (err) {
    console.error('RabbitMQ connection test failed:', err);
    throw err;
  } finally {
    if (connection) {
      await connection.close();
    }
  }
};

module.exports = {
  rabbitmqConfig,
  createConnection,
  setupChannel,
  publishMessage,
  testConnection
};
