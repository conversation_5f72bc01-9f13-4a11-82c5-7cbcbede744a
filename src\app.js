const express = require('express');
require('dotenv').config();

// Import routes
const assessmentRoutes = require('./api/routes');

// Import database connection test
const { testConnection: testDbConnection } = require('./config/db');
const { testConnection: testRabbitMQConnection } = require('./config/rabbitmq');

/**
 * Create Express application
 */
const createApp = () => {
  const app = express();
  
  // Trust proxy (important for getting real IP addresses behind load balancers)
  app.set('trust proxy', true);
  
  // Middleware for parsing JSON bodies
  app.use(express.json({
    limit: '10mb', // Limit request body size
    strict: true   // Only parse objects and arrays
  }));
  
  // Middleware for parsing URL-encoded bodies
  app.use(express.urlencoded({
    extended: true,
    limit: '10mb'
  }));
  
  // Security headers middleware
  app.use((req, res, next) => {
    // Remove X-Powered-By header for security
    res.removeHeader('X-Powered-By');
    
    // Add security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    
    next();
  });
  
  // Request logging middleware
  app.use((req, res, next) => {
    const start = Date.now();
    
    // Log request
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url} - IP: ${req.ip}`);
    
    // Log response when finished
    res.on('finish', () => {
      const duration = Date.now() - start;
      console.log(`${new Date().toISOString()} - ${req.method} ${req.url} - ${res.statusCode} - ${duration}ms`);
    });
    
    next();
  });
  
  // Mock JWT middleware (since API Gateway handles authentication)
  // In production, this would be replaced by actual JWT verification
  app.use((req, res, next) => {
    // For testing purposes, we'll mock the auth object
    // In production, the API Gateway would set this
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      // Mock user data - in production this would be parsed from JWT
      req.auth = {
        id: 'test-user-id', // This would come from the JWT payload
        email: '<EMAIL>'
      };
    }
    
    next();
  });
  
  // Mount assessment routes
  app.use('/', assessmentRoutes);
  
  // 404 handler
  app.use('*', (req, res) => {
    res.status(404).json({
      error: 'Not Found',
      message: `Route ${req.method} ${req.originalUrl} not found`,
      timestamp: new Date().toISOString()
    });
  });
  
  // Global error handler (must be last)
  app.use((err, req, res, next) => {
    console.error('Unhandled error:', {
      error: err.message,
      stack: err.stack,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    // Don't leak error details in production
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    res.status(err.status || 500).json({
      error: 'Internal Server Error',
      message: isDevelopment ? err.message : 'An unexpected error occurred',
      ...(isDevelopment && { stack: err.stack }),
      timestamp: new Date().toISOString()
    });
  });
  
  return app;
};

/**
 * Initialize application with health checks
 */
const initializeApp = async () => {
  try {
    console.log('Initializing Assessment Service...');
    
    // Test database connection
    console.log('Testing database connection...');
    await testDbConnection();
    
    // Test RabbitMQ connection
    console.log('Testing RabbitMQ connection...');
    await testRabbitMQConnection();
    
    console.log('All connections tested successfully');
    
    // Create Express app
    const app = createApp();
    
    console.log('Assessment Service initialized successfully');
    return app;
    
  } catch (error) {
    console.error('Failed to initialize Assessment Service:', error);
    throw error;
  }
};

/**
 * Graceful shutdown handler
 */
const gracefulShutdown = (server) => {
  return (signal) => {
    console.log(`Received ${signal}. Starting graceful shutdown...`);
    
    server.close((err) => {
      if (err) {
        console.error('Error during server shutdown:', err);
        process.exit(1);
      }
      
      console.log('Server closed successfully');
      
      // Close database connections
      const { pool } = require('./config/db');
      pool.end(() => {
        console.log('Database connections closed');
        process.exit(0);
      });
    });
    
    // Force shutdown after 30 seconds
    setTimeout(() => {
      console.error('Forced shutdown after timeout');
      process.exit(1);
    }, 30000);
  };
};

module.exports = {
  createApp,
  initializeApp,
  gracefulShutdown
};
