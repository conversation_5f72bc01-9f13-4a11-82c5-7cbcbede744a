#!/usr/bin/env node

/**
 * Assessment Service Server
 * Main entry point for the Assessment microservice
 */

require('dotenv').config();
const { initializeApp, gracefulShutdown } = require('./src/app');

// Configuration
const PORT = process.env.PORT || 3000;
const NODE_ENV = process.env.NODE_ENV || 'development';

/**
 * Start the server
 */
const startServer = async () => {
  try {
    console.log('='.repeat(50));
    console.log('🚀 Starting Assessment Service');
    console.log('='.repeat(50));
    console.log(`Environment: ${NODE_ENV}`);
    console.log(`Port: ${PORT}`);
    console.log(`Node Version: ${process.version}`);
    console.log(`Process ID: ${process.pid}`);
    console.log('='.repeat(50));
    
    // Initialize application
    const app = await initializeApp();
    
    // Start HTTP server
    const server = app.listen(PORT, () => {
      console.log('✅ Assessment Service is running');
      console.log(`📡 Server listening on port ${PORT}`);
      console.log(`🌐 Health check: http://localhost:${PORT}/health`);
      console.log('='.repeat(50));
      
      if (NODE_ENV === 'development') {
        console.log('📋 Available endpoints:');
        console.log(`   POST http://localhost:${PORT}/submit`);
        console.log(`   GET  http://localhost:${PORT}/status/:profileId`);
        console.log(`   GET  http://localhost:${PORT}/health`);
        console.log('='.repeat(50));
      }
    });
    
    // Handle server errors
    server.on('error', (error) => {
      if (error.syscall !== 'listen') {
        throw error;
      }
      
      const bind = typeof PORT === 'string' ? 'Pipe ' + PORT : 'Port ' + PORT;
      
      switch (error.code) {
        case 'EACCES':
          console.error(`❌ ${bind} requires elevated privileges`);
          process.exit(1);
          break;
        case 'EADDRINUSE':
          console.error(`❌ ${bind} is already in use`);
          process.exit(1);
          break;
        default:
          throw error;
      }
    });
    
    // Setup graceful shutdown handlers
    const shutdown = gracefulShutdown(server);
    process.on('SIGTERM', shutdown);
    process.on('SIGINT', shutdown);
    
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('❌ Uncaught Exception:', error);
      process.exit(1);
    });
    
    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
      process.exit(1);
    });
    
  } catch (error) {
    console.error('❌ Failed to start Assessment Service:', error);
    process.exit(1);
  }
};

// Start the server
startServer();
