# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_backend
DB_USER=assessment_user
DB_PASSWORD=your_password_here

# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_EXCHANGE=analysis_exchange
RABBITMQ_QUEUE=analysis_jobs_queue
RABBITMQ_ROUTING_KEY=analysis.job

# Application Settings
JWT_SECRET=your_jwt_secret_here
