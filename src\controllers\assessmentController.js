const { v4: uuidv4 } = require('uuid');
const { transaction } = require('../config/db');
const QueueService = require('../services/queueService');

/**
 * Assessment Controller
 * Handles the core business logic for assessment submission
 */
class AssessmentController {
  
  /**
   * Submit assessment for analysis
   * POST /submit
   */
  static async submitAssessment(req, res) {
    try {
      // Extract user ID from JWT payload (set by API Gateway)
      const userId = req.auth?.id || req.user?.id;
      
      if (!userId) {
        return res.status(401).json({
          error: 'User authentication required',
          message: 'User ID not found in request'
        });
      }
      
      // Extract assessment data from request body
      const {
        riasec_results,
        ocean_results,
        via_is_results,
        multiple_intelligences_results,
        cognitive_style_index_results
      } = req.body;
      
      console.log('Processing assessment submission for user:', userId);
      
      // Execute the entire workflow in a database transaction
      const result = await transaction(async (client) => {
        
        // Step 1: Check and reduce user quota with row locking
        const quotaQuery = `
          SELECT analysis_quota 
          FROM admin_schema.user_plans 
          WHERE user_id = $1 
          FOR UPDATE
        `;
        
        const quotaResult = await client.query(quotaQuery, [userId]);
        
        if (quotaResult.rows.length === 0) {
          throw new Error('User plan not found');
        }
        
        const currentQuota = quotaResult.rows[0].analysis_quota;
        
        if (currentQuota <= 0) {
          const error = new Error('Insufficient analysis quota');
          error.statusCode = 403;
          throw error;
        }
        
        // Step 2: Reduce quota by 1
        const updateQuotaQuery = `
          UPDATE admin_schema.user_plans 
          SET analysis_quota = analysis_quota - 1 
          WHERE user_id = $1
        `;
        
        await client.query(updateQuotaQuery, [userId]);
        
        console.log(`Quota reduced for user ${userId}. Remaining: ${currentQuota - 1}`);
        
        // Step 3: Create profile record
        const profileId = uuidv4();
        const rawInputData = {
          riasec_results,
          ocean_results,
          via_is_results,
          multiple_intelligences_results,
          cognitive_style_index_results
        };
        
        const insertProfileQuery = `
          INSERT INTO archive_schema.persona_profiles 
          (id, user_id, status, raw_input_data, created_at, updated_at)
          VALUES ($1, $2, $3, $4, NOW(), NOW())
          RETURNING id, created_at
        `;
        
        const profileResult = await client.query(insertProfileQuery, [
          profileId,
          userId,
          'processing',
          JSON.stringify(rawInputData)
        ]);
        
        console.log('Profile created successfully:', profileId);
        
        // Step 4: Log submission (optional)
        try {
          const logQuery = `
            INSERT INTO assessment_log_schema.submission_logs 
            (profile_id, user_id, submission_data, status, created_at)
            VALUES ($1, $2, $3, $4, NOW())
          `;
          
          await client.query(logQuery, [
            profileId,
            userId,
            JSON.stringify(rawInputData),
            'submitted'
          ]);
        } catch (logError) {
          // Log error but don't fail the transaction
          console.warn('Failed to log submission:', logError.message);
        }
        
        return {
          profileId,
          createdAt: profileResult.rows[0].created_at
        };
      });
      
      // Step 5: Publish to RabbitMQ (outside transaction)
      try {
        await QueueService.publishAssessmentJob(result.profileId, userId, {
          submissionTime: result.createdAt,
          dataTypes: [
            'riasec_results',
            'ocean_results', 
            'via_is_results',
            'multiple_intelligences_results',
            'cognitive_style_index_results'
          ]
        });
        
        console.log('Assessment job queued successfully:', result.profileId);
        
      } catch (queueError) {
        console.error('Failed to queue assessment job:', queueError.message);
        
        // Update profile status to indicate queue failure
        try {
          const updateStatusQuery = `
            UPDATE archive_schema.persona_profiles 
            SET status = $1, updated_at = NOW() 
            WHERE id = $2
          `;
          
          await transaction(async (client) => {
            await client.query(updateStatusQuery, ['queue_failed', result.profileId]);
          });
          
        } catch (updateError) {
          console.error('Failed to update profile status:', updateError.message);
        }
        
        // Return error to client
        return res.status(500).json({
          error: 'Failed to queue assessment for analysis',
          message: 'Assessment was saved but could not be queued for processing',
          profileId: result.profileId
        });
      }
      
      // Step 6: Return success response
      res.status(202).json({
        message: 'Assessment has been queued for analysis.',
        profileId: result.profileId
      });
      
    } catch (error) {
      console.error('Assessment submission error:', {
        userId: req.auth?.id || req.user?.id,
        error: error.message,
        stack: error.stack
      });
      
      // Handle specific error types
      if (error.statusCode === 403) {
        return res.status(403).json({
          error: 'Quota exceeded',
          message: 'You have insufficient analysis quota to perform this assessment'
        });
      }
      
      if (error.message.includes('User plan not found')) {
        return res.status(404).json({
          error: 'User plan not found',
          message: 'No valid subscription plan found for this user'
        });
      }
      
      // Generic server error
      res.status(500).json({
        error: 'Internal server error',
        message: 'An error occurred while processing your assessment'
      });
    }
  }
  
  /**
   * Get assessment status
   * GET /status/:profileId
   */
  static async getAssessmentStatus(req, res) {
    try {
      const { profileId } = req.params;
      const userId = req.auth?.id || req.user?.id;
      
      if (!userId) {
        return res.status(401).json({
          error: 'User authentication required'
        });
      }
      
      const query = `
        SELECT id, status, created_at, updated_at
        FROM archive_schema.persona_profiles 
        WHERE id = $1 AND user_id = $2
      `;
      
      const result = await require('../config/db').query(query, [profileId, userId]);
      
      if (result.rows.length === 0) {
        return res.status(404).json({
          error: 'Assessment not found',
          message: 'No assessment found with the provided ID'
        });
      }
      
      const profile = result.rows[0];
      
      res.json({
        profileId: profile.id,
        status: profile.status,
        createdAt: profile.created_at,
        updatedAt: profile.updated_at
      });
      
    } catch (error) {
      console.error('Get assessment status error:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to retrieve assessment status'
      });
    }
  }
}

module.exports = AssessmentController;
