{"name": "assessment-service", "version": "1.0.0", "description": "Assessment Service for ATMA Backend - handles assessment submission and queuing", "main": "server.js", "scripts": {"start": "node server.js", "dev": "NODE_ENV=development node server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["assessment", "microservice", "express", "rabbitmq", "postgresql"], "author": "ATMA Backend Team", "license": "ISC", "dependencies": {"amqplib": "^0.10.8", "dotenv": "^17.2.0", "express": "^5.1.0", "express-validator": "^7.2.1", "pg": "^8.16.3", "uuid": "^11.1.0"}}