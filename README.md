# Assessment Service

Assessment Service adalah microservice yang bertanggung jawab sebagai pintu masuk untuk semua data asesmen yang dikirim oleh pengguna dalam sistem ATMA Backend. Service ini menangani validasi, manajemen kuota, dan pengantrian tugas analisis.

## 🎯 Tujuan & Tanggung Jawab

- **Validasi Input**: Memastikan data JSON yang dikirim memiliki struktur yang benar
- **Manajemen Kuota**: Memeriksa dan mengurangi kuota analisis pengguna
- **Inisiasi & Pelacakan**: Membuat catatan profil asesmen di database
- **Pengantrean Tugas**: Mempublikasikan job analisis ke RabbitMQ
- **Respons Cepat**: Memberikan respons instan tanpa membuat pengguna menunggu

## 🏗️ Arsitektur

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   API Gateway   │───▶│ Assessment Service│───▶│   PostgreSQL    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                        ┌─────────────────┐
                        │    RabbitMQ     │
                        └─────────────────┘
```

## 📋 API Endpoints

### POST /submit
Menerima hasil tes lengkap dari pengguna dan mengantrekannya untuk analisis.

**Headers:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "riasec_results": { "R": 10, "I": 8, "A": 12, "S": 5, "E": 9, "C": 7 },
  "ocean_results": { "O": 4.5, "C": 3.8, "E": 4.1, "A": 4.7, "N": 2.1 },
  "via_is_results": { "creativity": 5, "curiosity": 4, "..." },
  "multiple_intelligences_results": { "linguistic": 8, "logical": 9, "..." },
  "cognitive_style_index_results": { "analytical": 7, "intuitive": 5 }
}
```

**Response (202 Accepted):**
```json
{
  "message": "Assessment has been queued for analysis.",
  "profileId": "uuid-profil-yang-baru-dibuat"
}
```

### GET /status/:profileId
Mendapatkan status asesmen berdasarkan profile ID.

**Response:**
```json
{
  "profileId": "uuid",
  "status": "processing",
  "createdAt": "2025-07-14T10:30:00Z",
  "updatedAt": "2025-07-14T10:30:00Z"
}
```

### GET /health
Health check endpoint untuk monitoring.

## 🛠️ Teknologi

- **Framework**: Express.js 5.x
- **Database**: PostgreSQL (dengan pg)
- **Message Broker**: RabbitMQ (dengan amqplib)
- **Validasi**: express-validator
- **Environment**: dotenv
- **UUID**: uuid

## 📁 Struktur Proyek

```
assessment-service/
├── src/
│   ├── api/
│   │   └── routes.js              # Definisi endpoint dan validasi
│   ├── config/
│   │   ├── db.js                  # Konfigurasi PostgreSQL
│   │   └── rabbitmq.js            # Konfigurasi RabbitMQ
│   ├── controllers/
│   │   └── assessmentController.js # Logika bisnis utama
│   ├── services/
│   │   └── queueService.js         # Service untuk RabbitMQ
│   └── app.js                     # Setup Express aplikasi
├── .env.example                   # Template environment variables
├── server.js                      # Entry point aplikasi
├── package.json
└── README.md
```

## ⚙️ Konfigurasi

Salin `.env.example` ke `.env` dan sesuaikan konfigurasi:

```bash
cp .env.example .env
```

**Environment Variables:**
```env
# Server
PORT=3000
NODE_ENV=development

# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_backend
DB_USER=assessment_user
DB_PASSWORD=your_password

# RabbitMQ
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_EXCHANGE=analysis_exchange
RABBITMQ_QUEUE=analysis_jobs_queue
RABBITMQ_ROUTING_KEY=analysis.job
```

## 🚀 Menjalankan Service

```bash
# Install dependencies
npm install

# Jalankan dalam mode development
npm run dev

# Jalankan dalam mode production
npm start
```

## 🔄 Alur Kerja

1. **Validasi Input**: Memvalidasi struktur JSON dan tipe data
2. **Ekstrak User ID**: Mengambil user ID dari JWT token
3. **Transaksi Database**:
   - Lock baris user_plans dengan `FOR UPDATE`
   - Periksa kuota analisis
   - Kurangi kuota jika tersedia
   - Buat record di persona_profiles
4. **Publikasi ke RabbitMQ**: Kirim job ke queue untuk diproses
5. **Respons**: Kirim profileId ke client

## 🗄️ Skema Database

**admin_schema.user_plans:**
- `user_id` (UUID)
- `analysis_quota` (INTEGER)

**archive_schema.persona_profiles:**
- `id` (UUID, Primary Key)
- `user_id` (UUID)
- `status` (VARCHAR)
- `raw_input_data` (JSONB)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

## 📊 Monitoring

- Health check: `GET /health`
- Logs: Console output dengan timestamp
- Error tracking: Structured error logging

## 🔒 Keamanan

- JWT token validation (handled by API Gateway)
- Input validation dengan express-validator
- SQL injection protection dengan parameterized queries
- Rate limiting (recommended at API Gateway level)

## 🧪 Testing

```bash
# Test endpoint dengan curl
curl -X POST http://localhost:3000/submit \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d @test-data.json
```

## 📝 Catatan Pengembangan

- Service ini tidak melakukan analisis, hanya menerima dan mengantrekan
- Semua operasi database menggunakan transaksi untuk konsistensi
- RabbitMQ connection dibuat per-request untuk reliability
- Error handling yang komprehensif untuk semua skenario
